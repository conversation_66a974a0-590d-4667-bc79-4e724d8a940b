[{"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/about/page.tsx": "1", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/proxy/[...path]/route.ts": "2", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/revalidate/route.ts": "3", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx": "4", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx": "5", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/chapters/[chapterId]/ReloadButton.tsx": "6", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/chapters/[chapterId]/page.tsx": "7", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/page.tsx": "8", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx": "9", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx": "10", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/api.ts": "11"}, {"size": 5654, "mtime": 1751131210778, "results": "12", "hashOfConfig": "13"}, {"size": 6595, "mtime": 1751131210780, "results": "14", "hashOfConfig": "13"}, {"size": 4444, "mtime": 1751131210780, "results": "15", "hashOfConfig": "13"}, {"size": 7014, "mtime": 1751131210779, "results": "16", "hashOfConfig": "13"}, {"size": 2460, "mtime": 1751131210781, "results": "17", "hashOfConfig": "13"}, {"size": 367, "mtime": 1751131210781, "results": "18", "hashOfConfig": "13"}, {"size": 7596, "mtime": 1751131210782, "results": "19", "hashOfConfig": "13"}, {"size": 6529, "mtime": 1751131210782, "results": "20", "hashOfConfig": "13"}, {"size": 2444, "mtime": 1751131210783, "results": "21", "hashOfConfig": "13"}, {"size": 4071, "mtime": 1751131210783, "results": "22", "hashOfConfig": "13"}, {"size": 8050, "mtime": 1751131210784, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "nmrpg4", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/about/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/proxy/[...path]/route.ts", ["57", "58"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/revalidate/route.ts", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx", ["59", "60", "61"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/chapters/[chapterId]/ReloadButton.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/chapters/[chapterId]/page.tsx", ["62"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/api.ts", ["63", "64", "65", "66", "67", "68", "69", "70", "71", "72", "73", "74", "75", "76"], [], {"ruleId": "77", "severity": 1, "message": "78", "line": 175, "column": 23, "nodeType": "79", "messageId": "80", "endLine": 175, "endColumn": 26, "suggestions": "81"}, {"ruleId": "82", "severity": 1, "message": "83", "line": 251, "column": 31, "nodeType": null, "messageId": "84", "endLine": 251, "endColumn": 39}, {"ruleId": "77", "severity": 1, "message": "78", "line": 8, "column": 50, "nodeType": "79", "messageId": "80", "endLine": 8, "endColumn": 53, "suggestions": "85"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 11, "column": 61, "nodeType": "79", "messageId": "80", "endLine": 11, "endColumn": 64, "suggestions": "86"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 11, "column": 74, "nodeType": "79", "messageId": "80", "endLine": 11, "endColumn": 77, "suggestions": "87"}, {"ruleId": "82", "severity": 1, "message": "88", "line": 29, "column": 12, "nodeType": null, "messageId": "84", "endLine": 29, "endColumn": 17}, {"ruleId": "77", "severity": 1, "message": "78", "line": 18, "column": 19, "nodeType": "79", "messageId": "80", "endLine": 18, "endColumn": 22, "suggestions": "89"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 70, "column": 10, "nodeType": "79", "messageId": "80", "endLine": 70, "endColumn": 13, "suggestions": "90"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 81, "column": 27, "nodeType": "79", "messageId": "80", "endLine": 81, "endColumn": 30, "suggestions": "91"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 91, "column": 28, "nodeType": "79", "messageId": "80", "endLine": 91, "endColumn": 31, "suggestions": "92"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 227, "column": 24, "nodeType": "79", "messageId": "80", "endLine": 227, "endColumn": 27, "suggestions": "93"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 234, "column": 25, "nodeType": "79", "messageId": "80", "endLine": 234, "endColumn": 28, "suggestions": "94"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 234, "column": 55, "nodeType": "79", "messageId": "80", "endLine": 234, "endColumn": 58, "suggestions": "95"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 241, "column": 24, "nodeType": "79", "messageId": "80", "endLine": 241, "endColumn": 27, "suggestions": "96"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 241, "column": 54, "nodeType": "79", "messageId": "80", "endLine": 241, "endColumn": 57, "suggestions": "97"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 248, "column": 27, "nodeType": "79", "messageId": "80", "endLine": 248, "endColumn": 30, "suggestions": "98"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 255, "column": 26, "nodeType": "79", "messageId": "80", "endLine": 255, "endColumn": 29, "suggestions": "99"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 255, "column": 56, "nodeType": "79", "messageId": "80", "endLine": 255, "endColumn": 59, "suggestions": "100"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 267, "column": 37, "nodeType": "79", "messageId": "80", "endLine": 267, "endColumn": 40, "suggestions": "101"}, {"ruleId": "77", "severity": 1, "message": "78", "line": 282, "column": 67, "nodeType": "79", "messageId": "80", "endLine": 282, "endColumn": 70, "suggestions": "102"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["103", "104"], "@typescript-eslint/no-unused-vars", "'_request' is defined but never used.", "unusedVar", ["105", "106"], ["107", "108"], ["109", "110"], "'error' is defined but never used.", ["111", "112"], ["113", "114"], ["115", "116"], ["117", "118"], ["119", "120"], ["121", "122"], ["123", "124"], ["125", "126"], ["127", "128"], ["129", "130"], ["131", "132"], ["133", "134"], ["135", "136"], ["137", "138"], {"messageId": "139", "fix": "140", "desc": "141"}, {"messageId": "142", "fix": "143", "desc": "144"}, {"messageId": "139", "fix": "145", "desc": "141"}, {"messageId": "142", "fix": "146", "desc": "144"}, {"messageId": "139", "fix": "147", "desc": "141"}, {"messageId": "142", "fix": "148", "desc": "144"}, {"messageId": "139", "fix": "149", "desc": "141"}, {"messageId": "142", "fix": "150", "desc": "144"}, {"messageId": "139", "fix": "151", "desc": "141"}, {"messageId": "142", "fix": "152", "desc": "144"}, {"messageId": "139", "fix": "153", "desc": "141"}, {"messageId": "142", "fix": "154", "desc": "144"}, {"messageId": "139", "fix": "155", "desc": "141"}, {"messageId": "142", "fix": "156", "desc": "144"}, {"messageId": "139", "fix": "157", "desc": "141"}, {"messageId": "142", "fix": "158", "desc": "144"}, {"messageId": "139", "fix": "159", "desc": "141"}, {"messageId": "142", "fix": "160", "desc": "144"}, {"messageId": "139", "fix": "161", "desc": "141"}, {"messageId": "142", "fix": "162", "desc": "144"}, {"messageId": "139", "fix": "163", "desc": "141"}, {"messageId": "142", "fix": "164", "desc": "144"}, {"messageId": "139", "fix": "165", "desc": "141"}, {"messageId": "142", "fix": "166", "desc": "144"}, {"messageId": "139", "fix": "167", "desc": "141"}, {"messageId": "142", "fix": "168", "desc": "144"}, {"messageId": "139", "fix": "169", "desc": "141"}, {"messageId": "142", "fix": "170", "desc": "144"}, {"messageId": "139", "fix": "171", "desc": "141"}, {"messageId": "142", "fix": "172", "desc": "144"}, {"messageId": "139", "fix": "173", "desc": "141"}, {"messageId": "142", "fix": "174", "desc": "144"}, {"messageId": "139", "fix": "175", "desc": "141"}, {"messageId": "142", "fix": "176", "desc": "144"}, {"messageId": "139", "fix": "177", "desc": "141"}, {"messageId": "142", "fix": "178", "desc": "144"}, "suggestUnknown", {"range": "179", "text": "180"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "181", "text": "182"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "183", "text": "180"}, {"range": "184", "text": "182"}, {"range": "185", "text": "180"}, {"range": "186", "text": "182"}, {"range": "187", "text": "180"}, {"range": "188", "text": "182"}, {"range": "189", "text": "180"}, {"range": "190", "text": "182"}, {"range": "191", "text": "180"}, {"range": "192", "text": "182"}, {"range": "193", "text": "180"}, {"range": "194", "text": "182"}, {"range": "195", "text": "180"}, {"range": "196", "text": "182"}, {"range": "197", "text": "180"}, {"range": "198", "text": "182"}, {"range": "199", "text": "180"}, {"range": "200", "text": "182"}, {"range": "201", "text": "180"}, {"range": "202", "text": "182"}, {"range": "203", "text": "180"}, {"range": "204", "text": "182"}, {"range": "205", "text": "180"}, {"range": "206", "text": "182"}, {"range": "207", "text": "180"}, {"range": "208", "text": "182"}, {"range": "209", "text": "180"}, {"range": "210", "text": "182"}, {"range": "211", "text": "180"}, {"range": "212", "text": "182"}, {"range": "213", "text": "180"}, {"range": "214", "text": "182"}, {"range": "215", "text": "180"}, {"range": "216", "text": "182"}, [3908, 3911], "unknown", [3908, 3911], "never", [215, 218], [215, 218], [339, 342], [339, 342], [352, 355], [352, 355], [299, 302], [299, 302], [1418, 1421], [1418, 1421], [1586, 1589], [1586, 1589], [1713, 1716], [1713, 1716], [4910, 4913], [4910, 4913], [5100, 5103], [5100, 5103], [5130, 5133], [5130, 5133], [5304, 5307], [5304, 5307], [5334, 5337], [5334, 5337], [5513, 5516], [5513, 5516], [5708, 5711], [5708, 5711], [5738, 5741], [5738, 5741], [5975, 5978], [5975, 5978], [6331, 6334], [6331, 6334]]