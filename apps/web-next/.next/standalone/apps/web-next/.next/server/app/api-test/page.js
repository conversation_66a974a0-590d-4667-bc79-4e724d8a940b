(()=>{var e={};e.id=868,e.ids=[868],e.modules={375:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1742:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(9572),a=s(9789),n=s(9751),i=s.n(n);let o={baseURL:"/api/proxy",timeout:3e4,retries:3};class l extends Error{constructor(e,t,s,r=!1){super(e),this.status=t,this.data=s,this.isRetryable=r,this.name="APIError"}static isRetryableError(e){return e>=500||!![408,429].includes(e)}getUserFriendlyMessage(){switch(this.status){case 400:return"請求參數錯誤，請檢查輸入內容";case 401:return"未授權訪問，請重新登入";case 403:return"權限不足，無法執行此操作";case 404:return"請求的資源不存在";case 408:return"請求超時，請稍後重試";case 429:return"請求過於頻繁，請稍後重試";case 500:return"伺服器內部錯誤，請稍後重試";case 502:return"網關錯誤，請稍後重試";case 503:return"服務暫時不可用，請稍後重試";case 504:return"網關超時，請稍後重試";default:return this.message||"發生未知錯誤"}}}async function d(e,t={}){let s,{method:r="GET",headers:a={},body:n,timeout:i=o.timeout,retries:c=o.retries}=t,p=e.startsWith("http")?e:`${o.baseURL}${e.startsWith("/")?e:`/${e}`}`,h={"Content-Type":"application/json",Accept:"application/json",...a};t.token&&(h.Authorization=`Bearer ${t.token}`),t.sessionCookie&&(h.Cookie=t.sessionCookie),t.isServerSide,h["X-Requested-With"]="SSR",n&&["POST","PUT","PATCH"].includes(r)&&(s="string"==typeof n?n:JSON.stringify(n));let x=null;for(let e=0;e<=c;e++)try{let e,t=new AbortController,a=setTimeout(()=>t.abort(),i),n=await fetch(p,{method:r,headers:h,body:s,signal:t.signal});clearTimeout(a);let o=n.headers.get("content-type");if(e=o?.includes("application/json")?await n.json():await n.text(),!n.ok){let t=l.isRetryableError(n.status);throw new l(`API request failed: ${n.statusText}`,n.status,e,t)}return{data:e,status:n.status,statusText:n.statusText,headers:n.headers}}catch(s){if(x=s,e===c)break;if(s instanceof TypeError||s instanceof DOMException){await new Promise(t=>setTimeout(t,1e3*(e+1)));continue}if(s instanceof l&&!s.isRetryable)throw s;let t=Math.min(1e3*Math.pow(2,e)+1e3*Math.random(),1e4);await new Promise(e=>setTimeout(e,t))}if(x instanceof l)throw x;throw new l(x?.message||"Unknown API error",0,x)}class c{static async get(e,t){return d(e,{...t,method:"GET"})}static async post(e,t,s){return d(e,{...s,method:"POST",body:t})}static async put(e,t,s){return d(e,{...s,method:"PUT",body:t})}static async delete(e,t){return d(e,{...t,method:"DELETE"})}static async patch(e,t,s){return d(e,{...s,method:"PATCH",body:t})}}let p={getList:e=>{let t=e?`?${new URLSearchParams(e).toString()}`:"";return c.get(`/novels${t}`)},getDetail:e=>c.get(`/novels/${e}`),getChapters:(e,t)=>{let s=t?`?${new URLSearchParams(t).toString()}`:"";return c.get(`/novels/${e}/chapters${s}`)}};function h(){let[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)(!1),o=(e,s,r,a)=>{t(t=>[...t,{test:e,success:s,data:r,error:a,timestamp:new Date().toLocaleTimeString()}])},l=async()=>{n(!0),t([]);try{try{let e=await c.get("/health");o("健康檢查",!0,e.data)}catch(e){o("健康檢查",!1,null,e)}try{let e=await p.getList({page:1,limit:5});o("小說列表 API",!0,e.data)}catch(e){o("小說列表 API",!1,null,e)}try{let e=await p.getDetail(1);o("小說詳情 API",!0,e.data)}catch(e){o("小說詳情 API",!1,null,e)}try{let e=await p.getChapters(1,{page:1,limit:5});o("章節列表 API",!0,e.data)}catch(e){o("章節列表 API",!1,null,e)}try{let e=await fetch("/api/proxy/novels"),t=await e.json();o("API 代理測試",e.ok,t)}catch(e){o("API 代理測試",!1,null,e)}}finally{n(!1)}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"API 連接測試"}),(0,r.jsx)(i(),{href:"/",className:"text-blue-600 hover:text-blue-800 transition-colors",children:"← 返回首頁"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg border border-blue-200",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-3 text-blue-900",children:"\uD83D\uDD27 API 代理測試工具"}),(0,r.jsx)("p",{className:"text-blue-800 mb-4",children:"這個頁面用於測試 Next.js 15 App Router 與 Django 後端的 API 連接。 點擊下方按鈕開始測試各個 API 端點。"}),(0,r.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,r.jsxs)("p",{children:["• 測試 API 代理路由: ",(0,r.jsx)("code",{className:"bg-blue-100 px-1 rounded",children:"/api/proxy/[...path]"})]}),(0,r.jsxs)("p",{children:["• 測試 Django 後端連接: ",(0,r.jsx)("code",{className:"bg-blue-100 px-1 rounded",children:"http://localhost:8000"})]}),(0,r.jsx)("p",{children:"• 測試 API 客戶端功能"}),(0,r.jsx)("p",{children:"• 驗證錯誤處理機制"})]})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("button",{onClick:l,disabled:s,className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:s?"測試中...":"開始 API 測試"}),(0,r.jsx)("button",{onClick:()=>{t([])},disabled:s,className:"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 disabled:bg-gray-100 disabled:cursor-not-allowed transition-colors",children:"清除結果"})]}),e.length>0&&(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-4 text-gray-900",children:"測試結果"}),(0,r.jsx)("div",{className:"space-y-4",children:e.map((e,t)=>(0,r.jsxs)("div",{className:`p-4 rounded-lg border ${e.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("h4",{className:`font-semibold ${e.success?"text-green-900":"text-red-900"}`,children:[e.success?"✅":"❌"," ",e.test]}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:e.timestamp})]}),e.success&&e.data&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("p",{className:"text-sm text-green-800 mb-2",children:"響應數據:"}),(0,r.jsx)("pre",{className:"bg-green-100 p-2 rounded text-xs overflow-x-auto",children:JSON.stringify(e.data,null,2)})]}),!e.success&&e.error&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("p",{className:"text-sm text-red-800 mb-2",children:"錯誤信息:"}),(0,r.jsx)("pre",{className:"bg-red-100 p-2 rounded text-xs overflow-x-auto",children:JSON.stringify(e.error,null,2)})]})]},t))})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg border",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-3 text-gray-900",children:"API 端點說明"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2 text-gray-700",children:"Django 後端 API"}),(0,r.jsxs)("ul",{className:"space-y-1 text-gray-600",children:[(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("code",{children:"/api/v1/health"})," - 健康檢查"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("code",{children:"/api/v1/novels"})," - 小說列表"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("code",{children:"/api/v1/novels/:id"})," - 小說詳情"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("code",{children:"/api/v1/novels/:id/chapters"})," - 章節列表"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2 text-gray-700",children:"Next.js API 代理"}),(0,r.jsxs)("ul",{className:"space-y-1 text-gray-600",children:[(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("code",{children:"/api/proxy/health"})," - 代理健康檢查"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("code",{children:"/api/proxy/novels"})," - 代理小說列表"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("code",{children:"/api/proxy/novels/:id"})," - 代理小說詳情"]}),(0,r.jsx)("li",{children:"• 支援所有 HTTP 方法 (GET, POST, PUT, DELETE)"})]})]})]})]})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3664:(e,t,s)=>{Promise.resolve().then(s.bind(s,1742))},3873:e=>{"use strict";e.exports=require("path")},4193:()=>{},4276:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o,metadata:()=>i});var r=s(1862),a=s(7221),n=s.n(a);s(4193);let i={title:{default:"瘟仙小說 - Next.js 15 App Router",template:"%s | 瘟仙小說"},description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",keywords:["小說","線上閱讀","Next.js","App Router"],authors:[{name:"NovelWebsite Team"}],creator:"NovelWebsite",publisher:"NovelWebsite",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL(process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:3001"),openGraph:{type:"website",locale:"zh_TW",url:"/",title:"瘟仙小說 - Next.js 15 App Router",description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",siteName:"瘟仙小說"},twitter:{card:"summary_large_image",title:"瘟仙小說 - Next.js 15 App Router",description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function o({children:e}){return(0,r.jsx)("html",{lang:"zh-TW",className:"h-full",children:(0,r.jsx)("body",{className:`${n().className} h-full bg-gray-50`,children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-4",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"瘟仙小說 (Next.js 15 App Router)"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"與 CRA 應用並行運行 - 端口: 3001"})]})}),(0,r.jsx)("main",{className:"max-w-6xl mx-auto px-4 py-6",children:e}),(0,r.jsx)("footer",{className:"bg-white border-t mt-12",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-4 py-6",children:(0,r.jsx)("p",{className:"text-center text-gray-600 text-sm",children:"\xa9 2025 瘟仙小說 - Next.js 15 App Router 版本"})})})]})})})}},4349:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,3859,23)),Promise.resolve().then(s.t.bind(s,7535,23)),Promise.resolve().then(s.t.bind(s,3555,23)),Promise.resolve().then(s.t.bind(s,7426,23)),Promise.resolve().then(s.t.bind(s,5558,23)),Promise.resolve().then(s.t.bind(s,634,23)),Promise.resolve().then(s.t.bind(s,4344,23)),Promise.resolve().then(s.t.bind(s,2194,23))},5103:()=>{},5193:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(950).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx","default")},6816:(e,t,s)=>{Promise.resolve().then(s.bind(s,5193))},7085:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6913,23)),Promise.resolve().then(s.t.bind(s,9481,23)),Promise.resolve().then(s.t.bind(s,9073,23)),Promise.resolve().then(s.t.bind(s,1424,23)),Promise.resolve().then(s.t.bind(s,2416,23)),Promise.resolve().then(s.t.bind(s,7060,23)),Promise.resolve().then(s.t.bind(s,7090,23)),Promise.resolve().then(s.t.bind(s,516,23))},8202:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(5406),a=s(1049),n=s(9073),i=s.n(n),o=s(430),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["api-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5193)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4276)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5559,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,7402,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,8467,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/api-test/page",pathname:"/api-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[67,847,751],()=>s(8202));module.exports=r})();
