<!DOCTYPE html><html lang="zh-TW" class="h-full"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/8a2a4b88271f26fb.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-d95d3426f4d7cbd8.js"/><script src="/_next/static/chunks/vendor-078a0c1b3f3dff9a.js" async=""></script><script src="/_next/static/chunks/common-bf83750cbe4eb6ad.js" async=""></script><script src="/_next/static/chunks/main-app-44be1214710eafaf.js" async=""></script><script src="/_next/static/chunks/app/novels/page-98b5c1b7b4a471bd.js" async=""></script><meta name="next-size-adjust" content=""/><title>小說列表 | 瘟仙小說</title><meta name="description" content="瀏覽所有可用的小說"/><meta name="author" content="NovelWebsite Team"/><meta name="keywords" content="小說,線上閱讀,Next.js,App Router"/><meta name="creator" content="NovelWebsite"/><meta name="publisher" content="NovelWebsite"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta name="format-detection" content="telephone=no, address=no, email=no"/><meta property="og:title" content="瘟仙小說 - Next.js 15 App Router"/><meta property="og:description" content="線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"/><meta property="og:url" content="http://localhost:3001"/><meta property="og:site_name" content="瘟仙小說"/><meta property="og:locale" content="zh_TW"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="瘟仙小說 - Next.js 15 App Router"/><meta name="twitter:description" content="線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c h-full bg-gray-50"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen bg-gray-50"><header class="bg-white shadow-sm"><div class="max-w-6xl mx-auto px-4 py-4"><h1 class="text-2xl font-bold text-gray-900">瘟仙小說 (Next.js 15 App Router)</h1><p class="text-sm text-gray-600 mt-1">與 CRA 應用並行運行 - 端口: 3001</p></div></header><main class="max-w-6xl mx-auto px-4 py-6"><div class="space-y-6"><div class="flex items-center justify-between"><h1 class="text-3xl font-bold text-gray-900">小說列表</h1><a class="text-blue-600 hover:text-blue-800 transition-colors" href="/">← 返回首頁</a></div><div class="bg-blue-50 p-6 rounded-lg border border-blue-200"><h2 class="text-xl font-semibold mb-3 text-blue-900">🚧 開發中</h2><p class="text-blue-800 mb-4">這個頁面正在開發中。未來將整合 Django 後端 API 來顯示小說列表。</p><div class="text-sm text-blue-700 space-y-1"><p>• 將使用 Next.js 15 的 Server Components</p><p>• 支援 SSR 和 SSG 以提升 SEO</p><p>• 整合現有的 Django API 端點</p><p>• 保持與 CRA 版本的功能一致性</p></div></div><div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6"><div class="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow"><div class="h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center"><span class="text-gray-500">封面圖片</span></div><h3 class="text-lg font-semibold mb-2 text-gray-900">示例小說 <!-- -->1</h3><p class="text-gray-600 text-sm mb-3">這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局...</p><div class="flex items-center justify-between text-sm text-gray-500"><span>作者: 示例作者</span><span>更新: 2025-01-01</span></div><a class="mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors" href="/novels/1">閱讀小說</a></div><div class="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow"><div class="h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center"><span class="text-gray-500">封面圖片</span></div><h3 class="text-lg font-semibold mb-2 text-gray-900">示例小說 <!-- -->2</h3><p class="text-gray-600 text-sm mb-3">這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局...</p><div class="flex items-center justify-between text-sm text-gray-500"><span>作者: 示例作者</span><span>更新: 2025-01-01</span></div><a class="mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors" href="/novels/2">閱讀小說</a></div><div class="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow"><div class="h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center"><span class="text-gray-500">封面圖片</span></div><h3 class="text-lg font-semibold mb-2 text-gray-900">示例小說 <!-- -->3</h3><p class="text-gray-600 text-sm mb-3">這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局...</p><div class="flex items-center justify-between text-sm text-gray-500"><span>作者: 示例作者</span><span>更新: 2025-01-01</span></div><a class="mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors" href="/novels/3">閱讀小說</a></div><div class="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow"><div class="h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center"><span class="text-gray-500">封面圖片</span></div><h3 class="text-lg font-semibold mb-2 text-gray-900">示例小說 <!-- -->4</h3><p class="text-gray-600 text-sm mb-3">這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局...</p><div class="flex items-center justify-between text-sm text-gray-500"><span>作者: 示例作者</span><span>更新: 2025-01-01</span></div><a class="mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors" href="/novels/4">閱讀小說</a></div><div class="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow"><div class="h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center"><span class="text-gray-500">封面圖片</span></div><h3 class="text-lg font-semibold mb-2 text-gray-900">示例小說 <!-- -->5</h3><p class="text-gray-600 text-sm mb-3">這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局...</p><div class="flex items-center justify-between text-sm text-gray-500"><span>作者: 示例作者</span><span>更新: 2025-01-01</span></div><a class="mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors" href="/novels/5">閱讀小說</a></div><div class="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow"><div class="h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center"><span class="text-gray-500">封面圖片</span></div><h3 class="text-lg font-semibold mb-2 text-gray-900">示例小說 <!-- -->6</h3><p class="text-gray-600 text-sm mb-3">這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局...</p><div class="flex items-center justify-between text-sm text-gray-500"><span>作者: 示例作者</span><span>更新: 2025-01-01</span></div><a class="mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors" href="/novels/6">閱讀小說</a></div></div></div><!--$--><!--/$--></main><footer class="bg-white border-t mt-12"><div class="max-w-6xl mx-auto px-4 py-6"><p class="text-center text-gray-600 text-sm">© 2025 瘟仙小說 - Next.js 15 App Router 版本</p></div></footer></div><script src="/_next/static/chunks/webpack-d95d3426f4d7cbd8.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[4602,[],\"\"]\n3:I[4216,[],\"\"]\n4:I[9839,[\"984\",\"static/chunks/app/novels/page-98b5c1b7b4a471bd.js\"],\"\"]\n5:I[9938,[],\"OutletBoundary\"]\n8:I[2658,[],\"AsyncMetadataOutlet\"]\na:I[9938,[],\"ViewportBoundary\"]\nc:I[9938,[],\"MetadataBoundary\"]\ne:I[7229,[],\"\"]\n:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/8a2a4b88271f26fb.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"LEOs9xiFB7RyhvHdwD9G8\",\"p\":\"\",\"c\":[\"\",\"novels\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"novels\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/8a2a4b88271f26fb.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"zh-TW\",\"className\":\"h-full\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c h-full bg-gray-50\",\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gray-50\",\"children\":[[\"$\",\"header\",null,{\"className\":\"bg-white shadow-sm\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-6xl mx-auto px-4 py-4\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-2xl font-bold text-gray-900\",\"children\":\"瘟仙小說 (Next.js 15 App Router)\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-gray-600 mt-1\",\"children\":\"與 CRA 應用並行運行 - 端口: 3001\"}]]}]}],[\"$\",\"main\",null,{\"className\":\"max-w-6xl mx-auto px-4 py-6\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"footer\",null,{\"className\":\"bg-white border-t mt-12\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-6xl mx-auto px-4 py-6\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-center text-gray-600 text-sm\",\"children\":\"© 2025 瘟仙小說 - Next.js 15 App Router 版本\"}]}]}]]}]}]}]]}],{\"children\":[\"novels\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"div\",null,{\"className\":\"space-y-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center justify-between\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-3xl font-bold text-gray-900\",\"children\":\"小說列表\"}],[\"$\",\"$L4\",null,{\"href\":\"/\",\"className\":\"text-blue-600 hover:text-blue-800 transition-colors\",\"children\":\"← 返回首頁\"}]]}],[\"$\",\"div\",null,{\"className\":\"bg-blue-50 p-6 rounded-lg border border-blue-200\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-xl font-semibold mb-3 text-blue-900\",\"children\":\"🚧 開發中\"}],[\"$\",\"p\",null,{\"className\":\"text-blue-800 mb-4\",\"children\":\"這個頁面正在開發中。未來將整合 Django 後端 API 來顯示小說列表。\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-blue-700 space-y-1\",\"children\":[[\"$\",\"p\",null,{\"children\":\"• 將使用 Next.js 15 的 Server Components\"}],[\"$\",\"p\",null,{\"children\":\"• 支援 SSR 和 SSG 以提升 SEO\"}],[\"$\",\"p\",null,{\"children\":\"• 整合現有的 Django API 端點\"}],[\"$\",\"p\",null,{\"children\":\"• 保持與 CRA 版本的功能一致性\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\",\"children\":[[\"$\",\"div\",\"1\",{\"className\":\"bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center\",\"children\":[\"$\",\"span\",null,{\"className\":\"text-gray-500\",\"children\":\"封面圖片\"}]}],[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold mb-2 text-gray-900\",\"children\":[\"示例小說 \",1]}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 text-sm mb-3\",\"children\":\"這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局...\"}],[\"$\",\"div\",null,{\"className\":\"flex items-center justify-between text-sm text-gray-500\",\"children\":[[\"$\",\"span\",null,{\"children\":\"作者: 示例作者\"}],[\"$\",\"span\",null,{\"children\":\"更新: 2025-01-01\"}]]}],[\"$\",\"$L4\",null,{\"href\":\"/novels/1\",\"className\":\"mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors\",\"children\":\"閱讀小說\"}]]}],[\"$\",\"div\",\"2\",{\"className\":\"bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center\",\"children\":[\"$\",\"span\",null,{\"className\":\"text-gray-500\",\"children\":\"封面圖片\"}]}],[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold mb-2 text-gray-900\",\"children\":[\"示例小說 \",2]}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 text-sm mb-3\",\"children\":\"這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局...\"}],[\"$\",\"div\",null,{\"className\":\"flex items-center justify-between text-sm text-gray-500\",\"children\":[[\"$\",\"span\",null,{\"children\":\"作者: 示例作者\"}],[\"$\",\"span\",null,{\"children\":\"更新: 2025-01-01\"}]]}],[\"$\",\"$L4\",null,{\"href\":\"/novels/2\",\"className\":\"mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors\",\"children\":\"閱讀小說\"}]]}],[\"$\",\"div\",\"3\",{\"className\":\"bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center\",\"children\":[\"$\",\"span\",null,{\"className\":\"text-gray-500\",\"children\":\"封面圖片\"}]}],[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold mb-2 text-gray-900\",\"children\":[\"示例小說 \",3]}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 text-sm mb-3\",\"children\":\"這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局...\"}],[\"$\",\"div\",null,{\"className\":\"flex items-center justify-between text-sm text-gray-500\",\"children\":[[\"$\",\"span\",null,{\"children\":\"作者: 示例作者\"}],[\"$\",\"span\",null,{\"children\":\"更新: 2025-01-01\"}]]}],[\"$\",\"$L4\",null,{\"href\":\"/novels/3\",\"className\":\"mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors\",\"children\":\"閱讀小說\"}]]}],[\"$\",\"div\",\"4\",{\"className\":\"bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center\",\"children\":[\"$\",\"span\",null,{\"className\":\"text-gray-500\",\"children\":\"封面圖片\"}]}],[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold mb-2 text-gray-900\",\"children\":[\"示例小說 \",4]}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 text-sm mb-3\",\"children\":\"這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局...\"}],[\"$\",\"div\",null,{\"className\":\"flex items-center justify-between text-sm text-gray-500\",\"children\":[[\"$\",\"span\",null,{\"children\":\"作者: 示例作者\"}],[\"$\",\"span\",null,{\"children\":\"更新: 2025-01-01\"}]]}],[\"$\",\"$L4\",null,{\"href\":\"/novels/4\",\"className\":\"mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors\",\"children\":\"閱讀小說\"}]]}],[\"$\",\"div\",\"5\",{\"className\":\"bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center\",\"children\":[\"$\",\"span\",null,{\"className\":\"text-gray-500\",\"children\":\"封面圖片\"}]}],[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold mb-2 text-gray-900\",\"children\":[\"示例小說 \",5]}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 text-sm mb-3\",\"children\":\"這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局...\"}],[\"$\",\"div\",null,{\"className\":\"flex items-center justify-between text-sm text-gray-500\",\"children\":[[\"$\",\"span\",null,{\"children\":\"作者: 示例作者\"}],[\"$\",\"span\",null,{\"children\":\"更新: 2025-01-01\"}]]}],[\"$\",\"$L4\",null,{\"href\":\"/novels/5\",\"className\":\"mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors\",\"children\":\"閱讀小說\"}]]}],[\"$\",\"div\",\"6\",{\"className\":\"bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center\",\"children\":[\"$\",\"span\",null,{\"className\":\"text-gray-500\",\"children\":\"封面圖片\"}]}],[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold mb-2 text-gray-900\",\"children\":[\"示例小說 \",6]}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 text-sm mb-3\",\"children\":\"這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局...\"}],[\"$\",\"div\",null,{\"className\":\"flex items-center justify-between text-sm text-gray-500\",\"children\":[[\"$\",\"span\",null,{\"children\":\"作者: 示例作者\"}],[\"$\",\"span\",null,{\"children\":\"更新: 2025-01-01\"}]]}],[\"$\",\"$L4\",null,{\"href\":\"/novels/6\",\"className\":\"mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors\",\"children\":\"閱讀小說\"}]]}]]}]]}],null,[\"$\",\"$L5\",null,{\"children\":[\"$L6\",\"$L7\",[\"$\",\"$L8\",null,{\"promise\":\"$@9\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"yJlpNxm9nUwzBcwfiBsGHv\",{\"children\":[[\"$\",\"$La\",null,{\"children\":\"$Lb\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$e\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"f:\"$Sreact.suspense\"\n10:I[2658,[],\"AsyncMetadata\"]\nd:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$f\",null,{\"fallback\":null,\"children\":[\"$\",\"$L10\",null,{\"promise\":\"$@11\"}]}]}]\n7:null\n"])</script><script>self.__next_f.push([1,"b:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n6:null\n"])</script><script>self.__next_f.push([1,"9:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"小說列表 | 瘟仙小說\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"瀏覽所有可用的小說\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"NovelWebsite Team\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"小說,線上閱讀,Next.js,App Router\"}],[\"$\",\"meta\",\"4\",{\"name\":\"creator\",\"content\":\"NovelWebsite\"}],[\"$\",\"meta\",\"5\",{\"name\":\"publisher\",\"content\":\"NovelWebsite\"}],[\"$\",\"meta\",\"6\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"7\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"8\",{\"name\":\"format-detection\",\"content\":\"telephone=no, address=no, email=no\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:title\",\"content\":\"瘟仙小說 - Next.js 15 App Router\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:description\",\"content\":\"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:url\",\"content\":\"http://localhost:3001\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:site_name\",\"content\":\"瘟仙小說\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:locale\",\"content\":\"zh_TW\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:title\",\"content\":\"瘟仙小說 - Next.js 15 App Router\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:description\",\"content\":\"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構\"}]],\"error\":null,\"digest\":\"$undefined\"}\n11:{\"metadata\":\"$9:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>