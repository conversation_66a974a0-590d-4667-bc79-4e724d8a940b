<!DOCTYPE html><html lang="zh-TW" class="h-full"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/8a2a4b88271f26fb.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-d95d3426f4d7cbd8.js"/><script src="/_next/static/chunks/vendor-078a0c1b3f3dff9a.js" async=""></script><script src="/_next/static/chunks/common-bf83750cbe4eb6ad.js" async=""></script><script src="/_next/static/chunks/main-app-44be1214710eafaf.js" async=""></script><script src="/_next/static/chunks/app/about/page-2f933b74cfd41fcf.js" async=""></script><meta name="next-size-adjust" content=""/><title>關於我們 | 瘟仙小說</title><meta name="description" content="了解 Next.js 15 App Router 版本的技術特色"/><meta name="author" content="NovelWebsite Team"/><meta name="keywords" content="小說,線上閱讀,Next.js,App Router"/><meta name="creator" content="NovelWebsite"/><meta name="publisher" content="NovelWebsite"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta name="format-detection" content="telephone=no, address=no, email=no"/><meta property="og:title" content="瘟仙小說 - Next.js 15 App Router"/><meta property="og:description" content="線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"/><meta property="og:url" content="http://localhost:3001"/><meta property="og:site_name" content="瘟仙小說"/><meta property="og:locale" content="zh_TW"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="瘟仙小說 - Next.js 15 App Router"/><meta name="twitter:description" content="線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c h-full bg-gray-50"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen bg-gray-50"><header class="bg-white shadow-sm"><div class="max-w-6xl mx-auto px-4 py-4"><h1 class="text-2xl font-bold text-gray-900">瘟仙小說 (Next.js 15 App Router)</h1><p class="text-sm text-gray-600 mt-1">與 CRA 應用並行運行 - 端口: 3001</p></div></header><main class="max-w-6xl mx-auto px-4 py-6"><div class="space-y-8"><div class="flex items-center justify-between"><h1 class="text-3xl font-bold text-gray-900">關於 Next.js 15 版本</h1><a class="text-blue-600 hover:text-blue-800 transition-colors" href="/">← 返回首頁</a></div><div class="prose max-w-none"><div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8 mb-8"><h2 class="text-2xl font-bold mb-4 text-white">技術架構升級</h2><p class="text-blue-100 text-lg">這是使用 Next.js 15 App Router 重新構建的版本，與現有 CRA 應用並行運行， 展示現代化前端架構的強大功能。</p></div><div class="grid md:grid-cols-2 gap-8"><div class="space-y-6"><h3 class="text-2xl font-semibold text-gray-900">🚀 核心特色</h3><div class="space-y-4"><div class="bg-white p-4 rounded-lg border"><h4 class="font-semibold text-gray-900 mb-2">App Router 架構</h4><p class="text-gray-600 text-sm">使用 Next.js 15 最新的 App Router，提供更好的開發體驗、 更強的類型安全和更靈活的佈局系統。</p></div><div class="bg-white p-4 rounded-lg border"><h4 class="font-semibold text-gray-900 mb-2">伺服器端渲染 (SSR)</h4><p class="text-gray-600 text-sm">支援 SSR 和 SSG，大幅提升 SEO 效果和首次載入速度， 解決 CRA 版本的 SEO 限制。</p></div><div class="bg-white p-4 rounded-lg border"><h4 class="font-semibold text-gray-900 mb-2">Monorepo 整合</h4><p class="text-gray-600 text-sm">完美整合 pnpm workspace 和 Turborepo， 支援共享套件和智能快取機制。</p></div></div></div><div class="space-y-6"><h3 class="text-2xl font-semibold text-gray-900">⚡ 性能優勢</h3><div class="space-y-4"><div class="bg-green-50 p-4 rounded-lg border border-green-200"><h4 class="font-semibold text-green-900 mb-2">自動程式碼分割</h4><p class="text-green-800 text-sm">Next.js 自動進行程式碼分割，只載入當前頁面需要的 JavaScript， 提升頁面載入速度。</p></div><div class="bg-green-50 p-4 rounded-lg border border-green-200"><h4 class="font-semibold text-green-900 mb-2">圖片優化</h4><p class="text-green-800 text-sm">內建圖片優化功能，自動轉換為 WebP 格式， 支援響應式圖片和懶載入。</p></div><div class="bg-green-50 p-4 rounded-lg border border-green-200"><h4 class="font-semibold text-green-900 mb-2">智能預載入</h4><p class="text-green-800 text-sm">自動預載入可見連結的頁面，提供近乎即時的頁面切換體驗。</p></div></div></div></div><div class="bg-yellow-50 p-6 rounded-lg border border-yellow-200 mt-8"><h3 class="text-xl font-semibold text-yellow-900 mb-3">🔄 並行運行策略</h3><div class="text-yellow-800 space-y-2"><p>• <strong>CRA 版本</strong>: 運行在端口 3000，保持現有功能</p><p>• <strong>Next.js 版本</strong>: 運行在端口 3001，展示新架構</p><p>• <strong>Django 後端</strong>: 運行在端口 8000，兩個前端共享</p><p>• <strong>漸進式遷移</strong>: 可以逐步將功能從 CRA 遷移到 Next.js</p></div></div><div class="bg-blue-50 p-6 rounded-lg border border-blue-200"><h3 class="text-xl font-semibold text-blue-900 mb-3">🛠️ 開發工具</h3><div class="grid md:grid-cols-2 gap-4 text-sm text-blue-800"><div><h4 class="font-semibold mb-2">前端技術棧</h4><ul class="space-y-1"><li>• Next.js 15.3.4</li><li>• React 18.3.1</li><li>• TypeScript 5.8.3</li><li>• Tailwind CSS 3.4.17</li><li>• Material-UI 6.4.12</li></ul></div><div><h4 class="font-semibold mb-2">開發工具</h4><ul class="space-y-1"><li>• pnpm workspace</li><li>• Turborepo 快取</li><li>• ESLint + Prettier</li><li>• 熱重載開發</li><li>• 自動類型檢查</li></ul></div></div></div></div></div><!--$--><!--/$--></main><footer class="bg-white border-t mt-12"><div class="max-w-6xl mx-auto px-4 py-6"><p class="text-center text-gray-600 text-sm">© 2025 瘟仙小說 - Next.js 15 App Router 版本</p></div></footer></div><script src="/_next/static/chunks/webpack-d95d3426f4d7cbd8.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[4602,[],\"\"]\n3:I[4216,[],\"\"]\n4:I[9839,[\"220\",\"static/chunks/app/about/page-2f933b74cfd41fcf.js\"],\"\"]\n5:I[9938,[],\"OutletBoundary\"]\n8:I[2658,[],\"AsyncMetadataOutlet\"]\na:I[9938,[],\"ViewportBoundary\"]\nc:I[9938,[],\"MetadataBoundary\"]\ne:I[7229,[],\"\"]\n:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/8a2a4b88271f26fb.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"LEOs9xiFB7RyhvHdwD9G8\",\"p\":\"\",\"c\":[\"\",\"about\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"about\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/8a2a4b88271f26fb.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"zh-TW\",\"className\":\"h-full\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c h-full bg-gray-50\",\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gray-50\",\"children\":[[\"$\",\"header\",null,{\"className\":\"bg-white shadow-sm\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-6xl mx-auto px-4 py-4\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-2xl font-bold text-gray-900\",\"children\":\"瘟仙小說 (Next.js 15 App Router)\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-gray-600 mt-1\",\"children\":\"與 CRA 應用並行運行 - 端口: 3001\"}]]}]}],[\"$\",\"main\",null,{\"className\":\"max-w-6xl mx-auto px-4 py-6\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"footer\",null,{\"className\":\"bg-white border-t mt-12\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-6xl mx-auto px-4 py-6\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-center text-gray-600 text-sm\",\"children\":\"© 2025 瘟仙小說 - Next.js 15 App Router 版本\"}]}]}]]}]}]}]]}],{\"children\":[\"about\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"div\",null,{\"className\":\"space-y-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center justify-between\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-3xl font-bold text-gray-900\",\"children\":\"關於 Next.js 15 版本\"}],[\"$\",\"$L4\",null,{\"href\":\"/\",\"className\":\"text-blue-600 hover:text-blue-800 transition-colors\",\"children\":\"← 返回首頁\"}]]}],[\"$\",\"div\",null,{\"className\":\"prose max-w-none\",\"children\":[[\"$\",\"div\",null,{\"className\":\"bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8 mb-8\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-2xl font-bold mb-4 text-white\",\"children\":\"技術架構升級\"}],[\"$\",\"p\",null,{\"className\":\"text-blue-100 text-lg\",\"children\":\"這是使用 Next.js 15 App Router 重新構建的版本，與現有 CRA 應用並行運行， 展示現代化前端架構的強大功能。\"}]]}],[\"$\",\"div\",null,{\"className\":\"grid md:grid-cols-2 gap-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"space-y-6\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-2xl font-semibold text-gray-900\",\"children\":\"🚀 核心特色\"}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"bg-white p-4 rounded-lg border\",\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-semibold text-gray-900 mb-2\",\"children\":\"App Router 架構\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 text-sm\",\"children\":\"使用 Next.js 15 最新的 App Router，提供更好的開發體驗、 更強的類型安全和更靈活的佈局系統。\"}]]}],[\"$\",\"div\",null,{\"className\":\"bg-white p-4 rounded-lg border\",\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-semibold text-gray-900 mb-2\",\"children\":\"伺服器端渲染 (SSR)\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 text-sm\",\"children\":\"支援 SSR 和 SSG，大幅提升 SEO 效果和首次載入速度， 解決 CRA 版本的 SEO 限制。\"}]]}],[\"$\",\"div\",null,{\"className\":\"bg-white p-4 rounded-lg border\",\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-semibold text-gray-900 mb-2\",\"children\":\"Monorepo 整合\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 text-sm\",\"children\":\"完美整合 pnpm workspace 和 Turborepo， 支援共享套件和智能快取機制。\"}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-6\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-2xl font-semibold text-gray-900\",\"children\":\"⚡ 性能優勢\"}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"bg-green-50 p-4 rounded-lg border border-green-200\",\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-semibold text-green-900 mb-2\",\"children\":\"自動程式碼分割\"}],[\"$\",\"p\",null,{\"className\":\"text-green-800 text-sm\",\"children\":\"Next.js 自動進行程式碼分割，只載入當前頁面需要的 JavaScript， 提升頁面載入速度。\"}]]}],[\"$\",\"div\",null,{\"className\":\"bg-green-50 p-4 rounded-lg border border-green-200\",\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-semibold text-green-900 mb-2\",\"children\":\"圖片優化\"}],[\"$\",\"p\",null,{\"className\":\"text-green-800 text-sm\",\"children\":\"內建圖片優化功能，自動轉換為 WebP 格式， 支援響應式圖片和懶載入。\"}]]}],[\"$\",\"div\",null,{\"className\":\"bg-green-50 p-4 rounded-lg border border-green-200\",\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-semibold text-green-900 mb-2\",\"children\":\"智能預載入\"}],[\"$\",\"p\",null,{\"className\":\"text-green-800 text-sm\",\"children\":\"自動預載入可見連結的頁面，提供近乎即時的頁面切換體驗。\"}]]}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"bg-yellow-50 p-6 rounded-lg border border-yellow-200 mt-8\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-xl font-semibold text-yellow-900 mb-3\",\"children\":\"🔄 並行運行策略\"}],[\"$\",\"div\",null,{\"className\":\"text-yellow-800 space-y-2\",\"children\":[[\"$\",\"p\",null,{\"children\":[\"• \",[\"$\",\"strong\",null,{\"children\":\"CRA 版本\"}],\": 運行在端口 3000，保持現有功能\"]}],[\"$\",\"p\",null,{\"children\":[\"• \",[\"$\",\"strong\",null,{\"children\":\"Next.js 版本\"}],\": 運行在端口 3001，展示新架構\"]}],[\"$\",\"p\",null,{\"children\":[\"• \",[\"$\",\"strong\",null,{\"children\":\"Django 後端\"}],\": 運行在端口 8000，兩個前端共享\"]}],[\"$\",\"p\",null,{\"children\":[\"• \",[\"$\",\"strong\",null,{\"children\":\"漸進式遷移\"}],\": 可以逐步將功能從 CRA 遷移到 Next.js\"]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"bg-blue-50 p-6 rounded-lg border border-blue-200\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-xl font-semibold text-blue-900 mb-3\",\"children\":\"🛠️ 開發工具\"}],[\"$\",\"div\",null,{\"className\":\"grid md:grid-cols-2 gap-4 text-sm text-blue-800\",\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-semibold mb-2\",\"children\":\"前端技術棧\"}],[\"$\",\"ul\",null,{\"className\":\"space-y-1\",\"children\":[[\"$\",\"li\",null,{\"children\":\"• Next.js 15.3.4\"}],[\"$\",\"li\",null,{\"children\":\"• React 18.3.1\"}],[\"$\",\"li\",null,{\"children\":\"• TypeScript 5.8.3\"}],[\"$\",\"li\",null,{\"children\":\"• Tailwind CSS 3.4.17\"}],[\"$\",\"li\",null,{\"children\":\"• Material-UI 6.4.12\"}]]}]]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h4\",null,{\"className\":\"font-semibold mb-2\",\"children\":\"開發工具\"}],[\"$\",\"ul\",null,{\"className\":\"space-y-1\",\"children\":[[\"$\",\"li\",null,{\"children\":\"• pnpm workspace\"}],[\"$\",\"li\",null,{\"children\":\"• Turborepo 快取\"}],[\"$\",\"li\",null,{\"children\":\"• ESLint + Prettier\"}],[\"$\",\"li\",null,{\"children\":\"• 熱重載開發\"}],[\"$\",\"li\",null,{\"children\":\"• 自動類型檢查\"}]]}]]}]]}]]}]]}]]}],null,[\"$\",\"$L5\",null,{\"children\":[\"$L6\",\"$L7\",[\"$\",\"$L8\",null,{\"promise\":\"$@9\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"oALOI7D5BxT3V8EzkU00Gv\",{\"children\":[[\"$\",\"$La\",null,{\"children\":\"$Lb\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$e\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"f:\"$Sreact.suspense\"\n10:I[2658,[],\"AsyncMetadata\"]\nd:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$f\",null,{\"fallback\":null,\"children\":[\"$\",\"$L10\",null,{\"promise\":\"$@11\"}]}]}]\n7:null\n"])</script><script>self.__next_f.push([1,"b:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n6:null\n"])</script><script>self.__next_f.push([1,"9:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"關於我們 | 瘟仙小說\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"了解 Next.js 15 App Router 版本的技術特色\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"NovelWebsite Team\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"小說,線上閱讀,Next.js,App Router\"}],[\"$\",\"meta\",\"4\",{\"name\":\"creator\",\"content\":\"NovelWebsite\"}],[\"$\",\"meta\",\"5\",{\"name\":\"publisher\",\"content\":\"NovelWebsite\"}],[\"$\",\"meta\",\"6\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"7\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"8\",{\"name\":\"format-detection\",\"content\":\"telephone=no, address=no, email=no\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:title\",\"content\":\"瘟仙小說 - Next.js 15 App Router\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:description\",\"content\":\"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:url\",\"content\":\"http://localhost:3001\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:site_name\",\"content\":\"瘟仙小說\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:locale\",\"content\":\"zh_TW\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:title\",\"content\":\"瘟仙小說 - Next.js 15 App Router\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:description\",\"content\":\"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構\"}]],\"error\":null,\"digest\":\"$undefined\"}\n11:{\"metadata\":\"$9:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>