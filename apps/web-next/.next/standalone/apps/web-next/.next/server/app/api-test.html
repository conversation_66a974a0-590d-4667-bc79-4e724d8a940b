<!DOCTYPE html><html lang="zh-TW" class="h-full"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/8a2a4b88271f26fb.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-d95d3426f4d7cbd8.js"/><script src="/_next/static/chunks/vendor-078a0c1b3f3dff9a.js" async=""></script><script src="/_next/static/chunks/common-bf83750cbe4eb6ad.js" async=""></script><script src="/_next/static/chunks/main-app-44be1214710eafaf.js" async=""></script><script src="/_next/static/chunks/app/api-test/page-354d3261c0d0a8db.js" async=""></script><meta name="next-size-adjust" content=""/><title>瘟仙小說 - Next.js 15 App Router</title><meta name="description" content="線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"/><meta name="author" content="NovelWebsite Team"/><meta name="keywords" content="小說,線上閱讀,Next.js,App Router"/><meta name="creator" content="NovelWebsite"/><meta name="publisher" content="NovelWebsite"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta name="format-detection" content="telephone=no, address=no, email=no"/><meta property="og:title" content="瘟仙小說 - Next.js 15 App Router"/><meta property="og:description" content="線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"/><meta property="og:url" content="http://localhost:3001"/><meta property="og:site_name" content="瘟仙小說"/><meta property="og:locale" content="zh_TW"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="瘟仙小說 - Next.js 15 App Router"/><meta name="twitter:description" content="線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c h-full bg-gray-50"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen bg-gray-50"><header class="bg-white shadow-sm"><div class="max-w-6xl mx-auto px-4 py-4"><h1 class="text-2xl font-bold text-gray-900">瘟仙小說 (Next.js 15 App Router)</h1><p class="text-sm text-gray-600 mt-1">與 CRA 應用並行運行 - 端口: 3001</p></div></header><main class="max-w-6xl mx-auto px-4 py-6"><div class="space-y-6"><div class="flex items-center justify-between"><h1 class="text-3xl font-bold text-gray-900">API 連接測試</h1><a class="text-blue-600 hover:text-blue-800 transition-colors" href="/">← 返回首頁</a></div><div class="bg-blue-50 p-6 rounded-lg border border-blue-200"><h2 class="text-xl font-semibold mb-3 text-blue-900">🔧 API 代理測試工具</h2><p class="text-blue-800 mb-4">這個頁面用於測試 Next.js 15 App Router 與 Django 後端的 API 連接。 點擊下方按鈕開始測試各個 API 端點。</p><div class="text-sm text-blue-700 space-y-1"><p>• 測試 API 代理路由: <code class="bg-blue-100 px-1 rounded">/api/proxy/[...path]</code></p><p>• 測試 Django 後端連接: <code class="bg-blue-100 px-1 rounded">http://localhost:8000</code></p><p>• 測試 API 客戶端功能</p><p>• 驗證錯誤處理機制</p></div></div><div class="flex gap-4"><button class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors">開始 API 測試</button><button class="bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 disabled:bg-gray-100 disabled:cursor-not-allowed transition-colors">清除結果</button></div><div class="bg-gray-50 p-6 rounded-lg border"><h3 class="text-lg font-semibold mb-3 text-gray-900">API 端點說明</h3><div class="grid md:grid-cols-2 gap-4 text-sm"><div><h4 class="font-semibold mb-2 text-gray-700">Django 後端 API</h4><ul class="space-y-1 text-gray-600"><li>• <code>/api/v1/health</code> - 健康檢查</li><li>• <code>/api/v1/novels</code> - 小說列表</li><li>• <code>/api/v1/novels/:id</code> - 小說詳情</li><li>• <code>/api/v1/novels/:id/chapters</code> - 章節列表</li></ul></div><div><h4 class="font-semibold mb-2 text-gray-700">Next.js API 代理</h4><ul class="space-y-1 text-gray-600"><li>• <code>/api/proxy/health</code> - 代理健康檢查</li><li>• <code>/api/proxy/novels</code> - 代理小說列表</li><li>• <code>/api/proxy/novels/:id</code> - 代理小說詳情</li><li>• 支援所有 HTTP 方法 (GET, POST, PUT, DELETE)</li></ul></div></div></div></div><!--$--><!--/$--></main><footer class="bg-white border-t mt-12"><div class="max-w-6xl mx-auto px-4 py-6"><p class="text-center text-gray-600 text-sm">© 2025 瘟仙小說 - Next.js 15 App Router 版本</p></div></footer></div><script src="/_next/static/chunks/webpack-d95d3426f4d7cbd8.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[4602,[],\"\"]\n3:I[4216,[],\"\"]\n4:I[9051,[],\"ClientPageRoot\"]\n5:I[4253,[\"868\",\"static/chunks/app/api-test/page-354d3261c0d0a8db.js\"],\"default\"]\n8:I[9938,[],\"OutletBoundary\"]\nb:I[2658,[],\"AsyncMetadataOutlet\"]\nd:I[9938,[],\"ViewportBoundary\"]\nf:I[9938,[],\"MetadataBoundary\"]\n11:I[7229,[],\"\"]\n:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/8a2a4b88271f26fb.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"NAr-K6ix81Tt2K1iPy1zH\",\"p\":\"\",\"c\":[\"\",\"api-test\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"api-test\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/8a2a4b88271f26fb.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"zh-TW\",\"className\":\"h-full\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c h-full bg-gray-50\",\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gray-50\",\"children\":[[\"$\",\"header\",null,{\"className\":\"bg-white shadow-sm\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-6xl mx-auto px-4 py-4\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-2xl font-bold text-gray-900\",\"children\":\"瘟仙小說 (Next.js 15 App Router)\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-gray-600 mt-1\",\"children\":\"與 CRA 應用並行運行 - 端口: 3001\"}]]}]}],[\"$\",\"main\",null,{\"className\":\"max-w-6xl mx-auto px-4 py-6\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"footer\",null,{\"className\":\"bg-white border-t mt-12\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-6xl mx-auto px-4 py-6\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-center text-gray-600 text-sm\",\"children\":\"© 2025 瘟仙小說 - Next.js 15 App Router 版本\"}]}]}]]}]}]}]]}],{\"children\":[\"api-test\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],null,[\"$\",\"$L8\",null,{\"children\":[\"$L9\",\"$La\",[\"$\",\"$Lb\",null,{\"promise\":\"$@c\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"MCzJ_oRFPNsu17sqGZhSVv\",{\"children\":[[\"$\",\"$Ld\",null,{\"children\":\"$Le\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:\"$Sreact.suspense\"\n13:I[2658,[],\"AsyncMetadata\"]\n6:{}\n7:{}\n10:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$12\",null,{\"fallback\":null,\"children\":[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]}]}]\n"])</script><script>self.__next_f.push([1,"a:null\n"])</script><script>self.__next_f.push([1,"e:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,"c:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"瘟仙小說 - Next.js 15 App Router\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"NovelWebsite Team\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"小說,線上閱讀,Next.js,App Router\"}],[\"$\",\"meta\",\"4\",{\"name\":\"creator\",\"content\":\"NovelWebsite\"}],[\"$\",\"meta\",\"5\",{\"name\":\"publisher\",\"content\":\"NovelWebsite\"}],[\"$\",\"meta\",\"6\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"7\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"8\",{\"name\":\"format-detection\",\"content\":\"telephone=no, address=no, email=no\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:title\",\"content\":\"瘟仙小說 - Next.js 15 App Router\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:description\",\"content\":\"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:url\",\"content\":\"http://localhost:3001\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:site_name\",\"content\":\"瘟仙小說\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:locale\",\"content\":\"zh_TW\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:title\",\"content\":\"瘟仙小說 - Next.js 15 App Router\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:description\",\"content\":\"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構\"}]],\"error\":null,\"digest\":\"$undefined\"}\n14:{\"metadata\":\"$c:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>