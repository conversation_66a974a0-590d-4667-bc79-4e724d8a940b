"""
Testing Settings for Monorepo + T3 Stack

Optimized for fast test execution with minimal external dependencies.
"""

import tempfile
from .base import *  # noqa: F403,F401

# Testing mode
DEBUG = False
TESTING = True

# Test hosts
ALLOWED_HOSTS = ["testserver", "localhost", "127.0.0.1"]

# CORS configuration for testing (permissive for test scenarios)
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://localhost:3001", 
    "http://testserver",
]

CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_ORIGINS = False

# CSRF configuration for testing
CSRF_TRUSTED_ORIGINS = [
    "http://localhost:3000",
    "http://localhost:3001",
    "http://testserver",
]

# Test database (in-memory SQLite for speed)
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": ":memory:",
        "OPTIONS": {
            "timeout": 20,
        },
    }
}

# Test cache (dummy cache for speed)
CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.dummy.DummyCache",
    }
}

# Test logging (minimal)
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
        },
    },
    "root": {
        "handlers": ["console"],
        "level": "CRITICAL",
    },
    "loggers": {
        "django": {
            "handlers": ["console"],
            "level": "CRITICAL",
            "propagate": False,
        },
    },
}

# Test email backend
EMAIL_BACKEND = "django.core.mail.backends.locmem.EmailBackend"

# Test file uploads (temporary directory)
MEDIA_ROOT = tempfile.mkdtemp()
FILE_UPLOAD_MAX_MEMORY_SIZE = 1024 * 1024  # 1MB

# Test security settings (relaxed for testing)
SECURE_SSL_REDIRECT = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
SECURE_CONTENT_TYPE_NOSNIFF = False
SECURE_BROWSER_XSS_FILTER = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# Test static files (no collection needed)
STATICFILES_STORAGE = "django.contrib.staticfiles.storage.StaticFilesStorage"

# Test REST Framework settings
REST_FRAMEWORK.update({  # noqa: F405
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
    ],
    "TEST_REQUEST_DEFAULT_FORMAT": "json",
})

# Test admin URL
ADMIN_URL = "admin/"

# Password hashers (fast for testing)
PASSWORD_HASHERS = [
    "django.contrib.auth.hashers.MD5PasswordHasher",
]

# Disable migrations for faster tests
class DisableMigrations:
    def __contains__(self, item):
        return True
    
    def __getitem__(self, item):
        return None

MIGRATION_MODULES = DisableMigrations()

# Test session configuration
SESSION_ENGINE = "django.contrib.sessions.backends.cache"

# Disable CSRF for API tests
CSRF_COOKIE_SECURE = False
CSRF_USE_SESSIONS = False

# Test-specific apps (if any)
TEST_APPS = []
INSTALLED_APPS += TEST_APPS  # noqa: F405
