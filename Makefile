# NovelWebsite Development Makefile
# 統一本地開發命令，確保所有指令在正確目錄執行
# 支援 Doppler 秘密管理
# CI 測試: 2025-06-21

.PHONY: help backend-dev backend-test backend-migrate backend-makemigrations backend-shell backend-collectstatic
.PHONY: frontend-dev frontend-test frontend-build frontend-lint frontend-type-check
.PHONY: dev test test-backend test-frontend test-integration test-e2e lint format clean install-deps
.PHONY: crawler-ttkan crawler-czbooks crawler-worker
.PHONY: ci-check docker-up docker-down
.PHONY: clean-docker clean-docker-all clean-act clean-all-containers
.PHONY: doppler-dev doppler-test doppler-migrate doppler-shell doppler-setup doppler-status _check_doppler_ready
.PHONY: ssh-runner ssh-runner-info infrastructure-status
.PHONY: build-golden-ami validate-packer init-packer
.PHONY: build-tier15-images test-tier15-images docker-build-ci docker-clean-ci

# 檢查是否有Doppler CLI和虛擬環境
DOPPLER := $(shell command -v doppler 2> /dev/null)
VENV_PATH := venv/bin/activate
ifdef DOPPLER
	DOPPLER_RUN = doppler run --
	VENV_DOPPLER_RUN = source $(VENV_PATH) && doppler run --
else
	DOPPLER_RUN =
	VENV_DOPPLER_RUN = source $(VENV_PATH) &&
endif

# 默認目標：顯示幫助
help:
	@echo "NovelWebsite 開發命令"
	@echo ""
	@echo "後端開發命令："
	@echo "  backend-dev         啟動Django開發伺服器"
	@echo "  backend-test        執行Django測試"
	@echo "  backend-migrate     執行資料庫遷移"
	@echo "  backend-makemigrations  建立新的遷移檔案"
	@echo "  backend-shell       啟動Django shell"
	@echo "  backend-collectstatic   收集靜態檔案"
	@echo ""
	@echo "前端開發命令："
	@echo "  frontend-dev        啟動React開發伺服器"
	@echo "  frontend-test       執行前端測試"
	@echo "  frontend-build      建置生產版本"
	@echo "  frontend-lint       執行ESLint檢查"
	@echo "  frontend-type-check 執行TypeScript類型檢查"
	@echo ""
	@echo "爬蟲命令："
	@echo "  crawler-ttkan       執行TTKAN爬蟲"
	@echo "  crawler-czbooks     執行CZBooks爬蟲"
	@echo "  crawler-worker      啟動爬蟲worker"
	@echo ""
	@echo "整合命令："
	@echo "  dev                 並行啟動後端和前端開發伺服器"
	@echo "  test                執行所有測試"
	@echo "  test-backend        僅執行後端測試"
	@echo "  test-frontend       僅執行前端測試"
	@echo "  test-integration    執行整合測試（程式碼層面）"
	@echo "  test-e2e            執行端到端(E2E)測試（需要運行環境）"
	@echo "  lint                執行所有檢查"
	@echo "  format              格式化所有程式碼"
	@echo "  install-deps        安裝所有依賴（包含開發依賴）"
	@echo "  install-dev-deps    僅安裝開發依賴"
	@echo "  ci-check            執行本地CI檢查"
	@echo ""
	@echo "🔐 Doppler 安全開發命令："
	@echo "  doppler-dev         使用Doppler運行完整開發環境"
	@echo "  doppler-test        使用Doppler運行所有測試"
	@echo "  doppler-migrate     使用Doppler執行資料庫遷移"
	@echo "  doppler-shell       使用Doppler啟動Django shell"
	@echo "  doppler-setup       快速設置Doppler並遷移環境變數"
	@echo "  doppler-status      檢查Doppler安裝和認證狀態"
	@echo ""
	@echo "Docker命令："
	@echo "  docker-up           啟動Docker services"
	@echo "  docker-down         停止Docker services"
	@echo ""
	@echo "🧹 Docker清理命令："
	@echo "  clean-docker        清理已停止的容器"
	@echo "  clean-docker-all    深度清理所有未使用的Docker資源"
	@echo "  clean-act           清理act測試相關容器和映像"
	@echo "  clean-all-containers 強制清理所有容器（慎用）"
	@echo ""
	@echo "🏗️ 基礎設施命令："
	@echo "  ssh-runner          連接到GitHub Actions Runner (EC2)"
	@echo "  ssh-runner-info     顯示Runner連接資訊"
	@echo "  infrastructure-status 檢查基礎設施狀態"
	@echo "  init-packer         初始化Packer環境"
	@echo "  validate-packer     驗證Packer配置"
	@echo "  build-golden-ami    構建黃金AMI映像"

# =====================================
# 後端開發命令
# =====================================

backend-dev:
	@echo "🚀 啟動Django開發伺服器..."
	cd backend && python manage.py runserver

backend-test:
	@echo "🧪 執行Django測試..."
	@make test-backend

backend-migrate:
	@echo "📊 執行資料庫遷移..."
	cd backend && python manage.py migrate

backend-makemigrations:
	@echo "📝 建立新的遷移檔案..."
	cd backend && python manage.py makemigrations

backend-shell:
	@echo "🐚 啟動Django shell..."
	cd backend && python manage.py shell

backend-collectstatic:
	@echo "📦 收集靜態檔案..."
	cd backend && python manage.py collectstatic --noinput

# =====================================
# 前端開發命令
# =====================================

frontend-dev:
	@echo "⚛️ 啟動React開發伺服器..."
	cd frontend && npm start

frontend-test:
	@echo "🧪 執行前端測試..."
	@make test-frontend

frontend-build:
	@echo "📦 建置前端生產版本..."
	cd frontend && npm run build

frontend-lint:
	@echo "🔍 執行ESLint檢查..."
	cd frontend && npm run lint

frontend-type-check:
	@echo "📝 執行TypeScript類型檢查..."
	cd frontend && npm run type-check

# =====================================
# 爬蟲命令
# =====================================

crawler-ttkan:
	@echo "🕷️ 執行TTKAN爬蟲..."
	cd backend && python manage.py crawl_ttkan

crawler-czbooks:
	@echo "🕷️ 執行CZBooks爬蟲..."
	cd backend && python manage.py crawl_czbooks

crawler-worker:
	@echo "⚙️ 啟動爬蟲worker..."
	cd backend && python novel/crawler/worker.py

# =====================================
# 整合命令
# =====================================

dev:
	@echo "🚀 並行啟動後端和前端開發伺服器..."
	@echo "使用 Ctrl+C 停止所有服務"
	@if command -v concurrently >/dev/null 2>&1; then \
		npx concurrently \
			--names "backend,frontend" \
			--prefix-colors "green,blue" \
			"make backend-dev" \
			"make frontend-dev"; \
	else \
		echo "⚠️ 需要安裝 concurrently: npm install -g concurrently"; \
		echo "或者分別在兩個終端執行:"; \
		echo "  make backend-dev"; \
		echo "  make frontend-dev"; \
	fi

test:
	@echo "🧪 執行所有測試..."
	@make test-backend
	@make test-frontend

test-backend:
	@echo "🐍 執行後端測試..."
	cd backend && source venv/bin/activate && DJANGO_SETTINGS_MODULE=config.minimal_settings python manage.py test --verbosity=2 --keepdb

test-frontend:
	@echo "⚛️ 執行前端測試..."
	cd frontend && npm test -- --coverage --watchAll=false --verbose

test-integration:
	@echo "🔗 執行整合測試（程式碼層面）..."
	@echo "📋 執行後端測試..."
	@make test-backend
	@echo "📋 執行前端測試..."
	@make test-frontend
	@echo "📋 執行前端整合測試..."
	cd frontend && npm test -- --testPathPattern=integration --coverage --watchAll=false
	@echo "✅ 整合測試完成！"
	@echo "💡 如需執行E2E測試，請使用: make test-e2e"

test-e2e:
	@echo "🎭 執行端到端 (E2E) 測試..."
	@echo "⚠️  請確保後端和前端開發伺服器正在運行 ('make dev')"
	@echo "⚠️  或者使用 docker-compose 啟動完整測試環境"
	@echo ""
	@read -p "確認環境已啟動？(y/N) " confirm && [ "$$confirm" = "y" ] || (echo "❌ 請先啟動開發環境，然後重新運行此命令" && exit 1)
	cd frontend && npx playwright test
	@echo "✅ E2E測試完成！"

lint:
	@echo "🔍 執行所有程式碼檢查..."
	@make frontend-lint
	@echo "📊 執行Python程式碼檢查..."
	cd backend && python -m flake8 . || echo "⚠️ 請先安裝 flake8: pip install flake8"

format:
	@echo "✨ 格式化所有程式碼..."
	@echo "📊 格式化Python程式碼..."
	cd backend && python -m black . || echo "⚠️ 請先安裝 black: pip install black"
	@echo "⚛️ 格式化前端程式碼..."
	cd frontend && npm run format || echo "⚠️ 請在frontend/package.json中添加format腳本"

install-deps:
	@echo "📦 安裝所有依賴..."
	@echo "📊 安裝後端依賴..."
	cd backend && pip install -r requirements.txt
	@echo "🛠️ 安裝後端開發依賴..."
	cd backend && pip install -r requirements-dev.txt
	@echo "⚛️ 安裝前端依賴..."
	cd frontend && npm ci
	@echo "🎭 安裝Playwright瀏覽器..."
	cd frontend && npx playwright install

ci-check:
	@echo "🔄 執行本地CI檢查..."
	@echo "📝 使用minimal_settings進行本地測試"
	DJANGO_SETTINGS_MODULE=config.minimal_settings ./scripts/testing/local-ci.sh

# =====================================
# 🛡️ 部署安全網測試命令
# =====================================

test-wsgi-smoke:
	@echo "🔥 Running WSGI Smoke Test..."
	@bash scripts/ci/wsgi-smoke-test.sh

test-legacy-scanner:
	@echo "🔍 Running Legacy Path Scanner..."
	@bash scripts/ci/legacy-path-scanner.sh --exclude="scripts/ci/regression-test.sh,backend/docs/migration_backup/,docs/_archive/,.cursorrules,backend/crawler_engine/scrapy.cfg"

test-deployment-simulation:
	@echo "🚀 Running Deployment Simulation Test..."
	@bash scripts/ci/deployment-simulation-test.sh

test-integration-safety:
	@echo "🔗 Running Integration Safety Test..."
	@bash scripts/ci/integration-safety-test.sh

test-regression:
	@echo "🧪 Running Regression Test..."
	@bash scripts/ci/regression-test.sh

test-safety-nets-all: test-wsgi-smoke test-legacy-scanner
	@echo "✅ All safety net tests passed!"

test-safety-nets-full: test-wsgi-smoke test-legacy-scanner test-deployment-simulation test-integration-safety test-regression
	@echo "🎉 Complete safety net test suite passed!"

install-dev-deps:
	@echo "🛠️ 僅安裝開發依賴..."
	cd backend && pip install -r requirements-dev.txt
	cd frontend && npx playwright install

# =====================================
# Docker命令
# =====================================

docker-up:
	@echo "🐳 啟動Docker services..."
	docker-compose up -d

docker-down:
	@echo "🐳 停止Docker services..."
	docker-compose down

# =====================================
# 🧹 Docker清理命令
# =====================================

clean-docker:
	@echo "🧹 清理已停止的Docker容器..."
	@docker container prune -f
	@echo "✅ 清理完成"

clean-docker-all:
	@echo "🧹 深度清理所有未使用的Docker資源..."
	@echo "⚠️  這會清理：已停止容器、未使用映像、未使用網路、構建緩存"
	@read -p "確認繼續？(y/N) " confirm && [ "$$confirm" = "y" ] || exit 1
	@docker system prune -a -f --volumes
	@echo "✅ 深度清理完成"

clean-act:
	@echo "🧹 清理act測試相關容器和映像..."
	@docker ps -a --filter "name=act-" --format "{{.ID}}" | xargs -r docker rm -f
	@docker images --filter "reference=catthehacker/ubuntu:act-*" --format "{{.ID}}" | xargs -r docker rmi -f
	@docker images --filter "reference=*act*" --format "{{.ID}}" | xargs -r docker rmi -f
	@echo "✅ Act清理完成"

clean-all-containers:
	@echo "⚠️  WARNING: 這會強制刪除所有容器（包括正在運行的）"
	@read -p "確認繼續？這是危險操作！(y/N) " confirm && [ "$$confirm" = "y" ] || exit 1
	@docker ps -aq | xargs -r docker rm -f
	@echo "✅ 所有容器已清理"

# =====================================
# 🔐 Doppler 安全開發命令
# =====================================

# 共用的 Doppler 檢查函數 (DRY 原則)
_check_doppler_ready:
	@if ! command -v doppler >/dev/null 2>&1; then \
		echo "❌ Doppler CLI未安裝，請先執行: brew install dopplerhq/cli/doppler"; \
		exit 1; \
	fi
	@if ! doppler me >/dev/null 2>&1; then \
		echo "❌ Doppler未認證，請先執行: doppler login"; \
		exit 1; \
	fi
	@echo "✅ Doppler 已就緒"

doppler-dev: _check_doppler_ready
	@echo "🔐 使用Doppler啟動完整開發環境..."
	@if command -v concurrently >/dev/null 2>&1; then \
		$(DOPPLER_RUN) npx concurrently \
			--names "backend,frontend" \
			--prefix-colors "green,blue" \
			"make backend-dev" \
			"make frontend-dev"; \
	else \
		echo "⚠️ 需要安裝 concurrently: npm install -g concurrently"; \
		echo "手動啟動後端: $(DOPPLER_RUN) make backend-dev"; \
		echo "手動啟動前端: $(DOPPLER_RUN) make frontend-dev"; \
	fi

doppler-test: _check_doppler_ready
	@echo "🔐 使用Doppler執行所有測試..."
	@$(DOPPLER_RUN) make test

doppler-migrate: _check_doppler_ready
	@echo "🔐 使用Doppler執行資料庫遷移..."
	@cd backend && $(VENV_DOPPLER_RUN) python manage.py migrate

doppler-shell: _check_doppler_ready
	@echo "🔐 使用Doppler啟動Django shell..."
	@cd backend && $(VENV_DOPPLER_RUN) python manage.py shell

# 快速Doppler設置命令
doppler-setup:
	@echo "🔐 快速設置Doppler..."
	@if [ -f "scripts/migrate-to-doppler.sh" ]; then \
		chmod +x scripts/migrate-to-doppler.sh; \
		./scripts/migrate-to-doppler.sh; \
	else \
		echo "❌ 找不到遷移腳本 scripts/migrate-to-doppler.sh"; \
	fi

# 檢查Doppler狀態
doppler-status:
	@echo "🔍 檢查Doppler狀態..."
	@if command -v doppler >/dev/null 2>&1; then \
		echo "✅ Doppler CLI已安裝: $$(doppler --version)"; \
		if doppler me >/dev/null 2>&1; then \
			echo "✅ Doppler已認證: $$(doppler me --json | grep email)"; \
			echo "📋 當前專案配置:"; \
			doppler configure; \
		else \
			echo "❌ Doppler未認證，請執行: doppler login"; \
		fi \
	else \
		echo "❌ Doppler CLI未安裝，請執行: brew install dopplerhq/cli/doppler"; \
	fi

# =====================================
# 清理命令
# =====================================

clean:
	@echo "🧹 清理快取和建置檔案..."
	@echo "清理Python快取..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	@echo "清理前端建置檔案..."
	rm -rf frontend/build/ frontend/node_modules/.cache/ 2>/dev/null || true
	@echo "清理Django快取..."
	cd backend && python manage.py flush --verbosity=0 --noinput 2>/dev/null || true
	@echo "✅ 清理完成"

# =====================================
# 快速啟動目標
# =====================================

# 快速啟動完整開發環境
quickstart: install-deps backend-migrate
	@echo "🚀 快速啟動完整開發環境..."
	@make dev

# =====================================
# 🏗️ 基礎設施命令
# =====================================

# GitHub Actions Runner 連接配置
RUNNER_PEM_KEY := $(HOME)/.ssh/novel-bastion-key-v2.pem
RUNNER_USER := ec2-user
RUNNER_HOST := **************

ssh-runner:
	@echo "🔗 連接到 GitHub Actions Runner..."
	@echo "主機: $(RUNNER_HOST)"
	@echo "用戶: $(RUNNER_USER)"
	@echo "金鑰: $(RUNNER_PEM_KEY)"
	@if [ ! -f "$(RUNNER_PEM_KEY)" ]; then \
		echo "❌ PEM 金鑰檔案不存在: $(RUNNER_PEM_KEY)"; \
		echo "請確認檔案路徑或檢查 AWS 金鑰配置"; \
		exit 1; \
	fi
	@ssh -i $(RUNNER_PEM_KEY) \
		-o StrictHostKeyChecking=no \
		-o UserKnownHostsFile=/dev/null \
		-o ConnectTimeout=10 \
		$(RUNNER_USER)@$(RUNNER_HOST)

ssh-runner-info:
	@echo "📋 GitHub Actions Runner 連接資訊:"
	@echo "  🖥️  主機: $(RUNNER_HOST)"
	@echo "  👤 用戶: $(RUNNER_USER)"
	@echo "  🔑 金鑰: $(RUNNER_PEM_KEY)"
	@echo "  📡 連接命令: ssh -i $(RUNNER_PEM_KEY) $(RUNNER_USER)@$(RUNNER_HOST)"
	@echo ""
	@echo "🔍 金鑰檔案狀態:"
	@if [ -f "$(RUNNER_PEM_KEY)" ]; then \
		echo "  ✅ PEM 金鑰存在"; \
		ls -la $(RUNNER_PEM_KEY); \
	else \
		echo "  ❌ PEM 金鑰不存在: $(RUNNER_PEM_KEY)"; \
	fi

infrastructure-status:
	@echo "🏗️ 檢查基礎設施狀態..."
	@echo ""
	@echo "📡 測試 Runner 連接性:"
	@if ssh -i $(RUNNER_PEM_KEY) \
		-o StrictHostKeyChecking=no \
		-o UserKnownHostsFile=/dev/null \
		-o ConnectTimeout=5 \
		-o BatchMode=yes \
		$(RUNNER_USER)@$(RUNNER_HOST) \
		"echo '✅ 連接成功' && hostname && uptime" 2>/dev/null; then \
		echo ""; \
		echo "🔍 檢查 GitHub Actions Runner 服務狀態:"; \
		ssh -i $(RUNNER_PEM_KEY) \
			-o StrictHostKeyChecking=no \
			-o UserKnownHostsFile=/dev/null \
			-o ConnectTimeout=5 \
			-o BatchMode=yes \
			$(RUNNER_USER)@$(RUNNER_HOST) \
			"cd actions-runner && sudo ./svc.sh status | head -10" 2>/dev/null || echo "❌ 無法檢查服務狀態"; \
	else \
		echo "❌ 無法連接到 Runner ($(RUNNER_HOST))"; \
		echo "請檢查:"; \
		echo "  1. EC2 實例是否運行中"; \
		echo "  2. 安全群組是否允許 SSH (port 22)"; \
		echo "  3. PEM 金鑰是否正確"; \
	fi

# Packer configuration
PACKER_DIR := infra/packer
PACKER_TEMPLATE := $(PACKER_DIR)/github-runner-al2023.pkr.hcl
PACKER_VARS := $(PACKER_DIR)/variables.pkrvars.hcl

init-packer:
	@echo "🔧 初始化 Packer 環境..."
	@if ! command -v packer >/dev/null 2>&1; then \
		echo "❌ Packer 未安裝。請安裝 Packer:"; \
		echo "  macOS: brew install packer"; \
		echo "  Linux: 參考 https://learn.hashicorp.com/tutorials/packer/get-started-install-cli"; \
		exit 1; \
	fi
	@echo "✅ Packer 版本: $$(packer version)"
	@echo "🔍 檢查 AWS 認證..."
	@if ! aws sts get-caller-identity >/dev/null 2>&1; then \
		echo "❌ AWS 認證失敗。請配置 AWS 認證:"; \
		echo "  aws configure"; \
		echo "  或設置環境變數 AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY"; \
		exit 1; \
	fi
	@echo "✅ AWS 認證成功: $$(aws sts get-caller-identity --query UserId --output text)"
	@echo "🔧 初始化 Packer plugins..."
	cd $(PACKER_DIR) && packer init $(PACKER_TEMPLATE)
	@echo "✅ Packer 環境初始化完成"

validate-packer: init-packer
	@echo "🔍 驗證 Packer 配置..."
	@if [ ! -f "$(PACKER_VARS)" ]; then \
		echo "⚠️ 變數檔案不存在，使用預設值"; \
		cd $(PACKER_DIR) && packer validate $(PACKER_TEMPLATE); \
	else \
		cd $(PACKER_DIR) && packer validate -var-file=$(shell basename $(PACKER_VARS)) $(PACKER_TEMPLATE); \
	fi
	@echo "✅ Packer 配置驗證通過"

build-golden-ami: validate-packer
	@echo "🏗️ 開始構建黃金 AMI..."
	@echo "⚠️ 這個過程可能需要 10-15 分鐘"
	@read -p "確認開始構建？(y/N) " confirm && [ "$$confirm" = "y" ] || exit 1
	@if [ ! -f "$(PACKER_VARS)" ]; then \
		echo "使用預設變數構建..."; \
		cd $(PACKER_DIR) && packer build $(PACKER_TEMPLATE); \
	else \
		echo "使用自定義變數構建..."; \
		cd $(PACKER_DIR) && packer build -var-file=$(shell basename $(PACKER_VARS)) $(PACKER_TEMPLATE); \
	fi
	@echo "✅ 黃金 AMI 構建完成！"
	@if [ -f "$(PACKER_DIR)/manifest.json" ]; then \
		echo "📋 構建清單:"; \
		cat $(PACKER_DIR)/manifest.json | jq -r '.builds[0] | "AMI ID: " + .artifact_id + "\nAMI Name: " + .custom_data.ami_name'; \
	fi

# =====================================
# Tier 1.5 Docker 映像管理
# =====================================

build-tier15-images:
	@echo "🏗️ 構建 Tier 1.5 優化映像..."
	@echo "📦 這將構建預安裝依賴的 CI/CD 映像"
	./infra/docker/build-images.sh

test-tier15-images:
	@echo "🧪 測試 Tier 1.5 映像..."
	@if ! docker images | grep -q "novel-web-frontend:ci-1.0"; then \
		echo "❌ 前端映像不存在，請先執行: make build-tier15-images"; \
		exit 1; \
	fi
	@if ! docker images | grep -q "novel-web-backend:ci-1.0"; then \
		echo "❌ 後端映像不存在，請先執行: make build-tier15-images"; \
		exit 1; \
	fi
	@echo "✅ 測試前端映像..."
	docker run --rm novel-web-frontend:ci-1.0 node --version
	docker run --rm novel-web-frontend:ci-1.0 npm --version
	@echo "✅ 測試後端映像..."
	docker run --rm novel-web-backend:ci-1.0 python --version
	docker run --rm novel-web-backend:ci-1.0 pip --version
	@echo "🎉 所有映像測試通過！"

docker-build-ci: build-tier15-images
	@echo "🚀 CI/CD 映像構建完成"

docker-clean-ci:
	@echo "🧹 清理 CI/CD 映像..."
	@if docker images | grep -q "novel-web"; then \
		docker rmi $$(docker images | grep "novel-web" | awk '{print $$3}') 2>/dev/null || true; \
		echo "✅ CI 映像已清理"; \
	else \
		echo "ℹ️ 沒有找到 CI 映像"; \
	fi
