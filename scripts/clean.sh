#!/bin/bash

# scripts/clean.sh
# 自動化 Next.js 構建產物清理腳本
# Issue #178: 自動化構建清理，減少手動 rm -rf 命令
# 用途：清理 Next.js 構建緩存、依賴、及臨時文件，減少磁盤佔用

set -euo pipefail

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 項目根目錄
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 計算目錄大小
get_dir_size() {
    local dir="$1"
    if [[ -d "$dir" ]]; then
        du -sh "$dir" 2>/dev/null | cut -f1 || echo "0B"
    else
        echo "0B"
    fi
}

# 安全刪除函數
safe_remove() {
    local target="$1"
    local description="$2"

    if [[ -e "$target" ]]; then
        local size
        size=$(get_dir_size "$target")
        log_info "正在清理 $description ($size)..."

        if rm -rf "$target" 2>/dev/null; then
            log_success "已清理 $description"
        else
            log_error "清理失敗: $description"
            return 1
        fi
    else
        log_info "跳過 $description (不存在)"
    fi
}

# 顯示使用方法
show_usage() {
    cat << EOF
使用方法: $0 [選項]

選項:
  --all, -a           清理所有構建產物和依賴 (預設)
  --build, -b         只清理構建產物 (.next, build, dist 等)
  --deps, -d          只清理依賴 (node_modules)
  --cache, -c         只清理緩存 (.turbo, .eslintcache 等)
  --logs, -l          只清理日誌文件
  --dry-run, -n       預覽將被清理的項目，但不實際刪除
  --help, -h          顯示此幫助信息

範例:
  $0                  # 清理所有構建產物
  $0 --build          # 只清理構建產物
  $0 --dry-run        # 預覽清理項目
  $0 --cache --logs   # 只清理緩存和日誌
EOF
}

# 預覽模式
dry_run=false
clean_all=true
clean_build=false
clean_deps=false
clean_cache=false
clean_logs=false

# 解析命令列參數
while [[ $# -gt 0 ]]; do
    case $1 in
        --all|-a)
            clean_all=true
            clean_build=false
            clean_deps=false
            clean_cache=false
            clean_logs=false
            ;;
        --build|-b)
            clean_all=false
            clean_build=true
            ;;
        --deps|-d)
            clean_all=false
            clean_deps=true
            ;;
        --cache|-c)
            clean_all=false
            clean_cache=true
            ;;
        --logs|-l)
            clean_all=false
            clean_logs=true
            ;;
        --dry-run|-n)
            dry_run=true
            ;;
        --help|-h)
            show_usage
            exit 0
            ;;
        *)
            log_error "未知參數: $1"
            show_usage
            exit 1
            ;;
    esac
    shift
done

# 如果指定了具體選項，則不執行全部清理
if [[ "$clean_build" == true || "$clean_deps" == true || "$clean_cache" == true || "$clean_logs" == true ]]; then
    clean_all=false
fi

# 定義清理目標 (使用索引陣列以兼容舊版 Bash)
CLEAN_PATHS=(
    "apps/web-next/.next"
    "apps/web-next/out"
    "apps/web-sunset/build"
    "apps/web-sunset/dist"
    "node_modules"
    "apps/web-next/node_modules"
    "apps/web-sunset/node_modules"
    "frontend/node_modules"
    "backend/venv"
    ".turbo"
    "apps/web-next/.turbo"
    "apps/web-sunset/.turbo"
    ".eslintcache"
    "apps/web-next/.eslintcache"
    "apps/web-sunset/.eslintcache"
    ".next/cache"
    "coverage"
    "apps/web-next/coverage"
    "apps/web-sunset/coverage"
    "frontend/coverage"
    "logs"
    "backend/logs"
    ".DS_Store"
)

CLEAN_DESCRIPTIONS=(
    "Next.js 構建輸出"
    "Next.js 靜態導出"
    "前端構建輸出"
    "前端分發包"
    "根目錄依賴"
    "Next.js 依賴"
    "前端依賴"
    "前端依賴 (legacy)"
    "Python 虛擬環境"
    "Turborepo 緩存"
    "Next.js Turbo 緩存"
    "前端 Turbo 緩存"
    "ESLint 緩存"
    "Next.js ESLint 緩存"
    "前端 ESLint 緩存"
    "Next.js 內部緩存"
    "測試覆蓋率報告"
    "Next.js 覆蓋率報告"
    "前端覆蓋率報告"
    "前端覆蓋率報告 (legacy)"
    "應用日誌"
    "後端日誌"
    "macOS 系統文件"
)

CLEAN_TYPES=(
    "build"
    "build"
    "build"
    "build"
    "deps"
    "deps"
    "deps"
    "deps"
    "deps"
    "cache"
    "cache"
    "cache"
    "cache"
    "cache"
    "cache"
    "cache"
    "build"
    "build"
    "build"
    "build"
    "logs"
    "logs"
    "system"
)

# 執行清理
main() {
    cd "$PROJECT_ROOT"

    log_info "開始清理 NovelWebsite 項目..."
    log_info "項目根目錄: $PROJECT_ROOT"

    if [[ "$dry_run" == true ]]; then
        log_warning "預覽模式 - 不會實際刪除文件"
    fi

    local total_cleaned=0
    local failed_count=0

    # 根據選項決定清理範圍
    for i in "${!CLEAN_PATHS[@]}"; do
        local target="${CLEAN_PATHS[$i]}"
        local description="${CLEAN_DESCRIPTIONS[$i]}"
        local type="${CLEAN_TYPES[$i]}"
        local should_clean=false

        if [[ "$clean_all" == true ]]; then
            should_clean=true
        else
            # 支援多個選項組合
            if [[ "$clean_build" == true ]] && [[ "$type" == "build" ]]; then
                should_clean=true
            fi
            if [[ "$clean_deps" == true ]] && [[ "$type" == "deps" ]]; then
                should_clean=true
            fi
            if [[ "$clean_cache" == true ]] && [[ "$type" == "cache" ]]; then
                should_clean=true
            fi
            if [[ "$clean_logs" == true ]] && [[ "$type" == "logs" ]]; then
                should_clean=true
            fi
        fi

        if [[ "$should_clean" == true ]]; then
            if [[ "$dry_run" == true ]]; then
                if [[ -e "$target" ]]; then
                    local size
                    size=$(get_dir_size "$target")
                    log_info "將清理: $description ($target) - $size"
                    ((total_cleaned++))
                fi
            else
                if safe_remove "$target" "$description"; then
                    ((total_cleaned++))
                else
                    ((failed_count++))
                fi
            fi
        fi
    done

    echo
    if [[ "$dry_run" == true ]]; then
        log_info "預覽完成 - 共 $total_cleaned 個項目將被清理"
        log_warning "執行 '$0' (不加 --dry-run) 來實際清理"
    else
        log_success "清理完成 - 成功清理 $total_cleaned 個項目"
        if [[ $failed_count -gt 0 ]]; then
            log_warning "清理失敗 $failed_count 個項目"
        fi

        # 顯示清理後的磁盤使用情況
        log_info "當前項目大小:"
        du -sh . 2>/dev/null || echo "無法計算項目大小"
    fi
}

# 信號處理
trap 'log_error "清理過程被中斷"; exit 1' INT TERM

# 執行主函數
main "$@"
